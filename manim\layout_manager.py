from manim import *

class LayoutManager:
    """布局管理器 - 避免元素重叠的智能布局系统"""

    @staticmethod
    def get_safe_position_right_of(left_object, right_object, margin=0.5):
        """计算右侧对象相对于左侧对象的安全位置"""
        left_right_edge = left_object.get_right()[0]
        right_width = right_object.width
        safe_x = left_right_edge + right_width/2 + margin
        return [safe_x, left_object.get_center()[1], 0]

    @staticmethod
    def get_safe_position_left_of(right_object, left_object, margin=0.5):
        """计算左侧对象相对于右侧对象的安全位置"""
        right_left_edge = right_object.get_left()[0]
        left_width = left_object.width
        safe_x = right_left_edge - left_width/2 - margin
        return [safe_x, right_object.get_center()[1], 0]

    @staticmethod
    def get_safe_position_above(bottom_object, top_object, margin=0.3):
        """计算上方对象相对于下方对象的安全位置"""
        bottom_top_edge = bottom_object.get_top()[1]
        top_height = top_object.height
        safe_y = bottom_top_edge + top_height/2 + margin
        return [bottom_object.get_center()[0], safe_y, 0]

    @staticmethod
    def get_safe_position_below(top_object, bottom_object, margin=0.3):
        """计算下方对象相对于上方对象的安全位置"""
        top_bottom_edge = top_object.get_bottom()[1]
        bottom_height = bottom_object.height
        safe_y = top_bottom_edge - bottom_height/2 - margin
        return [top_object.get_center()[0], safe_y, 0]

    @staticmethod
    def check_overlap(obj1, obj2, margin=0.1):
        """检查两个对象是否重叠（包含安全边距）"""
        # 使用get_left, get_right, get_top, get_bottom方法
        left1, right1 = obj1.get_left()[0], obj1.get_right()[0]
        top1, bottom1 = obj1.get_top()[1], obj1.get_bottom()[1]
        left2, right2 = obj2.get_left()[0], obj2.get_right()[0]
        top2, bottom2 = obj2.get_top()[1], obj2.get_bottom()[1]

        # 扩展边界以包含安全边距
        left1_expanded = left1 - margin
        right1_expanded = right1 + margin
        top1_expanded = top1 + margin
        bottom1_expanded = bottom1 - margin

        # 检查是否重叠
        return not (right1_expanded < left2 or
                   left1_expanded > right2 or
                   bottom1_expanded > top2 or
                   top1_expanded < bottom2)

    @staticmethod
    def print_layout_debug(obj, name):
        """打印布局调试信息"""
        center = obj.get_center()
        left, right = obj.get_left()[0], obj.get_right()[0]
        top, bottom = obj.get_top()[1], obj.get_bottom()[1]
        print(f"[布局调试] {name}: 中心=({center[0]:.2f}, {center[1]:.2f}), "
              f"边界=左{left:.2f} 右{right:.2f} 上{top:.2f} 下{bottom:.2f}")

    @staticmethod
    def ensure_screen_bounds(obj, screen_width=14, screen_height=8):
        """确保对象在屏幕边界内"""
        center = obj.get_center()
        left, right = obj.get_left()[0], obj.get_right()[0]
        top, bottom = obj.get_top()[1], obj.get_bottom()[1]

        # 检查并调整X坐标
        if left < -screen_width/2:
            center[0] = -screen_width/2 + obj.width/2
        elif right > screen_width/2:
            center[0] = screen_width/2 - obj.width/2

        # 检查并调整Y坐标
        if bottom < -screen_height/2:
            center[1] = -screen_height/2 + obj.height/2
        elif top > screen_height/2:
            center[1] = screen_height/2 - obj.height/2

        obj.move_to(center)
        return obj 