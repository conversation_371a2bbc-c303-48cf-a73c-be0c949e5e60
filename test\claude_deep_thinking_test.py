import asyncio
import sys
import os
import time
import json
from datetime import datetime

# 将项目根目录添加到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm.claude_local import ClaudeLocalProvider
from langchain_core.messages import SystemMessage, HumanMessage

async def test_deep_thinking():
    """测试Claude深度思考功能"""
    print("=" * 50)
    print("Claude深度思考功能测试")
    print("=" * 50)
    
    # 创建Claude本地提供者
    claude_provider = ClaudeLocalProvider(
        token_path="conf/c_token_file",
        model="claude-3-7-sonnet",
        max_tokens=36000
    )
    
    # 设置系统提示词
    system_prompt = "你是一个专业的问题分析专家，擅长深入思考复杂问题并给出详细的解决方案。"
    claude_provider.set_system_prompt(system_prompt)
    
    # 准备测试问题 - 这是一个需要深度思考的复杂问题
    complex_question = """
    请分析一个大型电商平台在双十一期间可能面临的系统架构挑战，并提出详细的解决方案。
    
    你的分析应该包括：
    1. 流量激增对系统各层的影响
    2. 数据库读写压力和缓存策略
    3. 订单处理和支付系统的高可用性保障
    4. 库存系统的实时性和一致性
    5. 分布式系统的容错和恢复机制
    6. 监控和报警系统的设计
    
    对于每个方面，请提供具体的技术方案和实施建议，包括可能的架构图和关键组件说明。
    """
    
    # 创建消息
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=complex_question)
    ]
    
    # 测试启用深度思考的情况
    print("\n[测试1] 启用深度思考模式:")
    start_time = time.time()
    response_with_thinking = await claude_provider.generate_response(
        messages=messages,
        enable_thinking=True,
        thinking_budget_tokens=16000
    )
    thinking_time = time.time() - start_time
    print(f"响应时间: {thinking_time:.2f}秒")
    print(f"响应长度: {len(response_with_thinking)} 字符")
    
    # 保存启用深度思考的响应
    with open("test_results_with_thinking.txt", "w", encoding="utf-8") as f:
        f.write(response_with_thinking)
    print("已将启用深度思考的响应保存到 test_results_with_thinking.txt")
    
    # 测试不启用深度思考的情况
    print("\n[测试2] 不启用深度思考模式:")
    start_time = time.time()
    response_without_thinking = await claude_provider.generate_response(
        messages=messages,
        enable_thinking=False
    )
    no_thinking_time = time.time() - start_time
    print(f"响应时间: {no_thinking_time:.2f}秒")
    print(f"响应长度: {len(response_without_thinking)} 字符")
    
    # 保存不启用深度思考的响应
    with open("test_results_without_thinking.txt", "w", encoding="utf-8") as f:
        f.write(response_without_thinking)
    print("已将不启用深度思考的响应保存到 test_results_without_thinking.txt")
    
    # 比较结果
    print("\n[结果比较]")
    print(f"启用深度思考响应时间: {thinking_time:.2f}秒")
    print(f"不启用深度思考响应时间: {no_thinking_time:.2f}秒")
    print(f"时间差异: {thinking_time - no_thinking_time:.2f}秒")
    print(f"启用深度思考响应长度: {len(response_with_thinking)} 字符")
    print(f"不启用深度思考响应长度: {len(response_without_thinking)} 字符")
    print(f"长度差异: {len(response_with_thinking) - len(response_without_thinking)} 字符")
    
    # 保存测试结果摘要
    results = {
        "测试时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "启用深度思考": {
            "响应时间(秒)": round(thinking_time, 2),
            "响应长度(字符)": len(response_with_thinking)
        },
        "不启用深度思考": {
            "响应时间(秒)": round(no_thinking_time, 2),
            "响应长度(字符)": len(response_without_thinking)
        },
        "差异": {
            "时间差异(秒)": round(thinking_time - no_thinking_time, 2),
            "长度差异(字符)": len(response_with_thinking) - len(response_without_thinking)
        }
    }
    
    with open("deep_thinking_test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print("已将测试结果摘要保存到 deep_thinking_test_results.json")

async def test_simple_thinking():
    """测试简单问题的深度思考功能"""
    print("\n" + "=" * 50)
    print("简单问题的深度思考功能测试")
    print("=" * 50)
    
    # 创建Claude本地提供者
    claude_provider = ClaudeLocalProvider(
        token_path="conf/c_token_file",
        model="claude-3-7-sonnet",
        max_tokens=36000
    )
    
    # 设置系统提示词
    system_prompt = "你是一个友好的助手。"
    claude_provider.set_system_prompt(system_prompt)
    
    # 准备测试问题 - 这是一个简单问题
    simple_question = "请解释什么是人工智能，并给出三个常见的应用场景。"
    
    # 创建消息
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=simple_question)
    ]
    
    # 测试启用深度思考的情况
    print("\n[测试3] 简单问题启用深度思考模式:")
    start_time = time.time()
    response = await claude_provider.generate_response(
        messages=messages,
        enable_thinking=True,
        thinking_budget_tokens=8000
    )
    thinking_time = time.time() - start_time
    print(f"响应时间: {thinking_time:.2f}秒")
    print(f"响应长度: {len(response)} 字符")
    
    # 保存响应
    with open("simple_question_with_thinking.txt", "w", encoding="utf-8") as f:
        f.write(response)
    print("已将简单问题启用深度思考的响应保存到 simple_question_with_thinking.txt")

async def main():
    """主函数"""
    # 测试深度思考功能
    await test_deep_thinking()
    
    # 测试简单问题的深度思考功能
    await test_simple_thinking()
    
    print("\n所有测试完成!")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
