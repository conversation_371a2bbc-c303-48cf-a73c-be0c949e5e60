import os
import uuid
import hashlib
import json
from typing import Dict, Any, List, Tuple
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager
from utils.prompt_utils import PromptManager
from utils.s3_utils import SVGSaveS3
from utils.db_utils import write_project_video_scene, write_project_video_scene_material
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor
from utils.db_pool import DBConnectionPool

class ManimGenerator:
    """Manim动画代码生成节点"""

    def __init__(self, conf_path="../conf/conf.ini"):
        """
        初始化Manim代码生成节点

        Args:
            conf_path: 配置文件路径
        """
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)
        self.conf_path = conf_path

        # 初始化S3存储工具
        self.s3_handler = SVGSaveS3(conf_path)

        # 初始化数据库连接池
        self.db_pool = DBConnectionPool(conf_path)

        # 创建Manim输出目录
        self.manim_output_dir = os.path.join("data", "manim_output")
        os.makedirs(self.manim_output_dir, exist_ok=True)

        # 生成唯一的项目ID
        self.project_id = None

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】Manim代码生成 (ManimGenerator)")
        print("="*50)

        try:
            # 初始化项目信息和目录
            project_dir, project_hash = self._init_project_info(state)

            # 执行批处理生成Manim代码
            results = await self._generate_manim_batch(state, project_hash, project_dir)

            # 保存脚本文件
            data_state_path = self._save_script_file(state, project_dir, project_hash)

            # 更新状态和保存结果
            scene_paths = self._update_state_with_results(state, results)

            # 添加项目路径到状态中，供后续节点使用
            state["data"]["project_dir"] = project_dir
            state["data"]["data_state_path"] = data_state_path
            state["data"]["scene_paths"] = scene_paths

            state["current_step"] = "generate_manim"

            print("="*50)
            print("【完成执行】Manim代码生成 (ManimGenerator)")
            print("="*50 + "\n")

            return state
        finally:
            # 这里不需要显式关闭连接池，因为连接会自动返回到池中
            pass

    def _init_project_info(self, state: Dict[str, Any]) -> Tuple[str, str]:
        """
        初始化项目信息和目录

        Args:
            state: 当前状态

        Returns:
            Tuple[str, str]: 项目目录路径和项目哈希
        """
        # 获取热词和标题信息
        trend_word = state["data"].get("trend_word", "未知热词")
        title = state["data"].get("news_report", {}).get("title", "未知标题")
        print(f"热词: {trend_word}, 标题: {title}")

        # 生成唯一的项目标识符
        project_hash = hashlib.md5(f"{trend_word}".encode()).hexdigest()
        project_dir = f"manim_video/{project_hash}"
        print(f"项目目录: {project_dir}")

        # 创建项目目录
        os.makedirs(os.path.join(self.manim_output_dir, project_hash), exist_ok=True)

        return project_dir, project_hash

    async def _generate_manim_batch(self, state: Dict[str, Any], project_hash: str, project_dir: str) -> List[Tuple[str, str, str]]:
        """
        执行批处理生成Manim代码

        Args:
            state: 当前状态
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            List[Tuple[str, str, str]]: 生成结果列表，每项包含(场景ID, Manim代码, S3路径)
        """
        # 从news_report中获取SVG提示词
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])

        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景和镜头
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"

            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])

            if subsections:
                # 如果有子镜头，则处理每个子镜头的svg_prompt
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    svg_prompt = subsection.get("svg_prompt")
                    if svg_prompt:
                        batch_items.append((shot_id, svg_prompt))
                        print(f"准备处理场景 {section_id} 的镜头 {j+1} 的Manim代码生成")
            else:
                # 如果没有子镜头，则处理整个场景的svg_prompt
                svg_prompt = section.get("svg_prompt")
                if svg_prompt:
                    batch_items.append((section_id, svg_prompt))
                    print(f"准备处理场景 {i+1} 的Manim代码生成")

        print(f"准备批处理 {len(batch_items)} 个场景的Manim代码生成")

        # 定义一个异步的包装函数来处理响应
        async def process_manim_response_wrapper(item, response):
            return await self._process_manim_response(item, response, project_hash, project_dir)

        # 执行批处理（启用深度思考，使用temperature=0.0以获得确定性输出）
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=self._create_messages_for_manim,
            process_response_func=process_manim_response_wrapper,
            max_retries=5,
            temperature=0.0
        )

        return results