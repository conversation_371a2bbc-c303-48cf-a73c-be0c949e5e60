# -*- coding: utf-8 -*-
import asyncio
import aiohttp
import json
from typing import Optional, Dict, Any, List
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from llm.base import LLMProvider


class ClaudeLocalProvider(LLMProvider):
    """<PERSON>本地模型提供者"""
    # BASE_URL = "http://10.185.17.118:8000/completion"
    BASE_URL = "http://i.aigc.weibo.com/completion"
    SOURCE = "**********"  # Default appkey

    def __init__(self, token_path: str = "conf/c_token_file", model: str = "claude-3-7-sonnet", max_tokens: int = 128000):
        """
        初始化Claude本地模型

        Args:
            token_path: 认证token文件路径
            model: Claude模型名称
            max_tokens: 最大生成的token数量
        """
        self.token_path = token_path
        self.model = model
        self.max_tokens = max_tokens
        self.appkey = self.SOURCE
        self.system_prompt = None
        # 创建一个共享的会话，提高性能
        self._session = None

    def set_system_prompt(self, system_prompt: str):
        """
        设置系统提示词

        Args:
            system_prompt: 系统提示词
        """
        self.system_prompt = system_prompt

    def get_header(self) -> Dict[str, str]:
        """
        从token文件获取认证头信息

        Returns:
            包含Authorization的headers字典
        """
        try:
            with open(self.token_path, encoding='utf-8') as fr:
                line = fr.readline().strip()
                # 检查行内容是否包含冒号
                if ":" in line:
                    c_tauth_token = line.split(":", 1)[1].strip()
                    auth_header = {"Authorization": c_tauth_token}
                else:
                    # 如果没有冒号，则假设整行就是token
                    auth_header = {"Authorization": line}

                # 添加Accept和Content-Type头，明确告诉服务器我们期望JSON格式
                headers = {
                    **auth_header,
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
                return headers
        except Exception as e:
            print(f"获取认证头信息失败: {e}")
            raise

    async def get_session(self) -> aiohttp.ClientSession:
        """
        获取或创建一个aiohttp会话

        Returns:
            aiohttp.ClientSession: HTTP会话
        """
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession()
        return self._session

    async def close_session(self):
        """关闭aiohttp会话"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None

    async def call_api_async(self,
                params: Dict[str, Any],
                payload: Dict[str, Any],
                try_count: int = 1) -> Optional[Dict]:
        """
        异步通用API调用方法

        Args:
            params: 请求参数
            payload: 请求体数据
            try_count: 重试次数

        Returns:
            API响应结果
        """
        # 确保params中包含必要参数
        if "appkey" not in params:
            params["appkey"] = self.appkey

        attempts = try_count
        while attempts > 0:
            try:
                headers = self.get_header()
                print(f"BASE_URL: {self.BASE_URL}")

                # 检查请求头大小
                header_size = len(str(headers))
                if header_size > 8000:  # 设置一个合理的请求头大小限制
                    print(f"警告: 请求头过大 ({header_size} 字节)，可能导致413错误")

                # 添加重试间隔，避免连接重置
                if attempts < try_count:
                    # 使用指数退避策略，每次重试等待时间增加
                    wait_time = 2 * (try_count - attempts + 1)
                    print(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)  # 重试前等待

                # 获取会话
                session = await self.get_session()

                # 发送异步请求
                async with session.post(
                    self.BASE_URL,
                    headers=headers,
                    params=params,
                    json=payload,
                    timeout=86400
                ) as response:

                    # 检查响应状态码
                    if response.status != 200:
                        text = await response.text()
                        print(f"HTTP错误: {response.status} - {text}")
                        if response.status == 413:
                            print("请求实体过大，尝试减小请求大小")
                            attempts -= 1
                            continue

                    # 直接获取响应内容，不检查Content-Type
                    response_text = await response.text()

                    # 尝试解析JSON响应
                    try:
                        # 使用已经获取的文本内容直接解析JSON
                        result = json.loads(response_text)
                        print(result)
                        if result.get("code") != 200:
                            error_msg = result.get("message", "Unknown error")
                            print(f"API Error: {error_msg}")
                            attempts -= 1
                            continue
                        return result
                    except ValueError as json_err:
                        print(f"JSON解析错误: {json_err}")
                        print(f"响应内容: {response_text[:200]}...")
                        attempts -= 1
                        continue

            except aiohttp.ClientError as conn_err:
                print(f"连接错误: {conn_err}")
                print("服务器连接被重置，尝试重新连接...")
                attempts -= 1
            except Exception as e:
                print(f"API调用异常: {e}")
                attempts -= 1

        return None

    async def claude_async(self,
           messages: List[BaseMessage],
           model: str = None,
           max_tokens: int = 128000,
           try_count: int = 1,
           enable_thinking: bool = False,
           thinking_budget_tokens: int = 16000,
           temperature: float = 1.0) -> Optional[Dict]:
        """异步调用Claude API进行文本生成

        Args:
            messages: 消息列表，包含SystemMessage和HumanMessage
            model: Claude模型名称，如果为None则使用实例默认模型
            max_tokens: 最大生成的token数量
            try_count: 重试次数
            enable_thinking: 是否启用思考模式
            thinking_budget_tokens: 思考模式的token预算
            temperature: 温度参数，控制生成文本的随机性，默认为1.0

        Returns:
            Claude API响应结果
        """
        # 如果没有指定模型，使用实例的默认模型
        if model is None:
            model = self.model
            
        # 转换消息格式
        api_messages = []
        for message in messages:
            if isinstance(message, SystemMessage):
                # 系统消息已经在payload中设置
                continue
            elif isinstance(message, HumanMessage):
                api_messages.append({
                    "role": "user",
                    "content": message.content
                })

        params = {'message': 'placeholder', 'appkey': self.appkey, 'type': 'aws', 'model_id': model, 'use_ext_first': '1'}

        # 构建基本payload
        model_ext = {
            "system": self.system_prompt,
            "messages": api_messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "anthropic_version": "bedrock-2023-05-31"
        }


        # 如果启用思考模式，添加thinking配置
        if enable_thinking:
            model_ext["thinking"] = {
                "type": "enabled",
                "budget_tokens": thinking_budget_tokens
            }

        payload = {
            "model_ext": model_ext
        }

        print("model_ext payload====:", payload)

        return await self.call_api_async(params, payload, try_count)

    async def generate_response(self, messages: List[BaseMessage], enable_thinking: bool = False, thinking_budget_tokens: int = 16000, temperature: float = 1.0) -> str:
        """
        异步生成响应

        Args:
            messages: 消息列表
            enable_thinking: 是否启用思考模式
            thinking_budget_tokens: 思考模式的token预算
            temperature: 温度参数，控制生成文本的随机性，默认为1.0

        Returns:
            str: 生成的响应文本，API的原始返回
        """
        # 提取系统提示词
        system_prompt = None
        for message in messages:
            if isinstance(message, SystemMessage):
                system_prompt = message.content
                break

        # 设置系统提示词
        if system_prompt:
            self.set_system_prompt(system_prompt)

        # 异步调用Claude API
        result = await self.claude_async(
            messages=messages,
            model=None,  # 使用provider的默认模型
            max_tokens=self.max_tokens,
            try_count=5,  # 增加重试次数为5次
            enable_thinking=enable_thinking,
            thinking_budget_tokens=thinking_budget_tokens,
            temperature=temperature,
        )

        if result and result.get("code") == 200:
            response_data = result.get("response_data", {})
            content = response_data.get("content", [])

            # 直接提取文本内容
            if content and isinstance(content, list):
                for item in content:
                    if item.get("type") == "text":
                        # 直接返回原始文本，不做任何解析
                        return item.get('text', "")

        return "fail"

    def create_messages(self,
                       system_prompt: str,
                       history: List[BaseMessage],
                       query: str) -> List[BaseMessage]:
        """
        创建消息列表

        Args:
            system_prompt: 系统提示词
            history: 历史消息
            query: 用户查询

        Returns:
            List[BaseMessage]: 消息列表
        """
        messages = [SystemMessage(content=system_prompt)]
        messages.extend(history)
        messages.append(HumanMessage(content=query))
        return messages


async def call_claude_api_async(text, token_path="conf/c_token_file", max_tokens=128000, try_count=1):
    """
    异步使用微博API客户端调用Claude服务

    Args:
        text: 用户输入文本
        token_path: 认证token文件路径
        max_tokens: 最大生成的token数量
        try_count: 重试次数

    Returns:
        str: Claude API响应的文本内容
    """
    # 创建ClaudeLocalProvider实例
    provider = ClaudeLocalProvider(token_path=token_path)

    # 构建消息
    messages = [
        SystemMessage(content="You are a helpful assistant."),
        HumanMessage(content=text)
    ]

    # 异步调用Claude API
    result = await provider.claude_async(
        messages=messages,
        model=None,  # 使用provider的默认模型
        max_tokens=max_tokens,
        try_count=max(try_count, 5),  # 确保至少有5次重试
    )

    if result and result.get("code") == 200:
        response_data = result.get("response_data", {})
        content = response_data.get("content", [])

        # 提取文本内容
        if content and isinstance(content, list):
            for item in content:
                if item.get("type") == "text":
                    response_text = item.get('text')
                    print(f"Claude response: {response_text}")
                    # Save the response to claude_response.txt file
                    with open("claude_response.txt", "a", encoding="utf-8") as f:
                        f.write(f"{response_text}\n\n")
                    return response_text
    return None


# 为了向后兼容，保留同步方法，但内部使用异步实现
def call_claude_api(text, token_path="conf/c_token_file", max_tokens=128000, try_count=1):
    """
    使用微博API客户端调用Claude服务（同步版本，内部使用异步实现）

    Args:
        text: 用户输入文本
        token_path: 认证token文件路径
        max_tokens: 最大生成的token数量
        try_count: 重试次数

    Returns:
        str: Claude API响应的文本内容
    """
    # 使用asyncio运行异步函数
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(call_claude_api_async(text, token_path, max_tokens, try_count))
