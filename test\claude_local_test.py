import asyncio
import sys
import os

# 将项目根目录添加到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm.claude_local import ClaudeLocalProvider
from llm.conversation_manager import ConversationManager

async def main():
    """主函数"""
    # 创建Claude本地提供者
    claude_local_provider = ClaudeLocalProvider(
        token_path="conf/c_token_file",
        model="claude-3-7-sonnet",
        max_tokens=36000
    )

    # 创建对话管理器
    conversation = ConversationManager(provider=claude_local_provider)
    conversation.set_system_prompt("你是一个AI提示词专家，擅长生成 svg 动画代码")

    # 获取响应（启用思考模式）
    response = await conversation.get_response(
        query="""
        # SVG动画设计文档生成提示词\n\n你是一位专业的SVG动画设计师和技术文档撰写专家。请根据用户提供的输入内容，创建一个详细的SVG动画设计文档，遵循以下格式和要求。\n\n## 输入\n1. **text**：事故发生后，贵州省立即启动应急预案，调集248名消防员，其中包括83名专业潜水员赶赴现场。救援人员面临的是极为复杂的情况：船体倒扣在水面，部分未正确穿戴救生衣的游客被困船舱；湍急的江水不断将落水者冲向下游；天色渐暗，增加了搜寻难度。\'我们到达现场时，能见度极低，水下搜救几乎只能靠触摸，\'一位参与救援的潜水员事后回忆。令人感动的是，附近村民闻讯后立即划船参与救援，在专业力量到达前挽救了多名落水者。救援队伍沿乌江下游50公里设立了多个拦截点，同时调用无人机、热成像设备和救援直升机进行全方位搜寻。值得一提的是，一名女船长在船只侧翻前的果断操作，成功将自己负责的船只驶向避风区域，保护了全船乘客安全，被誉为事故中的英雄。\n2. **props**：{\'characterAssets\': [{\'name\': \'专业潜水员\', \'description\': \' 身着专业潜水装备的救援人员，正在进行水下搜救任务。他们在低能见度的水下环境中主要靠触摸寻找被困人员。\', \'aspectRatio\': \'3:4\', \'image_type\': \'jpg\', \'path\': \'data/asset/characters/专业潜水员.jpg\'}, {\'name\': \'消防救援人员\', \'description\': \'身着橙色或红色救援服的消防员，在岸边或船上进行救援协调和支援工作，装备齐全，表情严肃专注。\', \'aspectRatio\': \'3:4\', \'image_type\': \'jpg\', \'path\': \'data/asset/characters/消防救援人员.jpg\'}, {\'name\': \'女船长\', \'description\': \'临危不乱的女船长，身着船员制服，表情坚定，正在驾驶船只避开危险区域，被誉为事故中的英雄。\', \'aspectRatio\': \'3:4\', \'image_type\': \'jpg\', \'path\': \'data/asset/characters/女船长.jpg\'}, {\'name\': \'当地村民救援者\', \'description\': \'身着普通衣物的当地村民，划着小船参与救援工作，展现了紧急情况下的民间力量和团结精神。\', \'aspectRatio\': \'3:4\', \'image_type\': \'jpg\', \'path\': \'data/asset/characters/当地村民救援者.jpg\'}, {\'name\': \'被困游客\', \'description\': \'在船舱内被困或在水中挣扎的游客，有些未正确穿戴救生衣，表情惊恐，等待救援。\', \'aspectRatio\': \'3:4\', \'image_type\': \'jpg\', \'path\': \'data/asset/characters/被困游客.jpg\'}], \'propAssets\': [{\'name\': \'倒扣船只\', \'description\': \'在乌江水面上倒扣的游船，显示事故现场的严峻情况，部分船体露出水面。\', \'aspectRatio\': \'16:9\', \'image_type\': \'jpg\', \'path\': \'data/asset/props/倒扣船只.jpg\'}, {\'name\': \'救援直升机\', \'description\': \'在事故水域上空盘旋的救援直升机，配备搜救设备，参与空中救援和侦查工作。\', \'aspectRatio\': \'16:9\', \'image_type\': \'jpg\', \'path\': \'data/asset/props/救援直升机.jpg\'}, {\'name\': \'救援无人机\', \'description\': \'配备热成像设备的救援无人机，在夜间低空飞行搜寻落水人员。\', \'aspectRatio\': \'4:3\', \'image_type\': \'jpg\', \'path\': \'data/asset/props/救援无人机.jpg\'}, {\'name\': \'村民救援小船\', \'description\': \'当地村民用于参与救援的传统小木船或小渔船，简单但在紧急情况下发挥重要作用。\', \'aspectRatio\': \'16:9\', \'image_type\': \'jpg\', \'path\': \'data/asset/props/村民救援小船.jpg\'}, {\'name\': \'热成像设备\', \'description\': \'救援人员手持的热成像设备，用于在能见度 低的环境中搜寻生命体征。\', \'aspectRatio\': \'1:1\', \'image_type\': \'jpg\', \'path\': \'data/asset/props/热成像设备.jpg\'}, {\'name\': \'救生衣\', \'description\': \'橙色或红色的标准救生衣，事故中一些游客未正确穿戴这一关键安全装备。\', \'aspectRatio\': \'3:4\', \'image_type\': \'png\', \'path\': \'data/asset/props/救生衣.png\'}, {\'name\': \'乌江湍急水域\', \'description\': \'湍急的乌江水域，波浪汹涌，体现救援环境的危险和困难。\', \'aspectRatio\': \'16:9\', \'image_type\': \'jpg\', \'path\': \'data/asset/props/乌江湍急水域.jpg\'}, {\'name\': \'救援拦截点\', \'description\': \'沿乌江下游设立的救援拦截点，包括岸边的帐篷、设备和待命的救援人员。\', \'aspectRatio\': \'16:9\', \'image_type\': \'jpg\', \'path\': \'data/asset/props/救援拦截点.jpg\'}, {\'name\': \'潜水装备\', \'description\': \'专业潜水员使用的潜水装备，包括氧气罐、潜水面罩、防水手电筒等，用于水下搜救行动。\', \'aspectRatio\': \'3:4\', \'image_type\': \'png\', \'path\': \'data/asset/props/潜水装备.png\'}, {\'name\': \'安全避风区域\', \'description\': \'乌江上相对平静的避风区域，是女船长 成功驶入保护乘客的安全地带。\', \'aspectRatio\': \'16:9\', \'image_type\': \'jpg\', \'path\': \'data/asset/props/安全避风区域.jpg\'}]}\n\n## 输出要求\n\n请生成一个完整的SVG动画设计文档，文件名为`generate_svg.md`，包含以下所有部分：\n\n### 1. 整体概念\n- 基于输入的text创建一个5秒钟动画的概念描述\n- 保持 简洁明了，突出核心信息\n\n### 2. 画布尺寸与格式\n- 尺寸：1920×1080像素（16:9高清格式）\n- 帧率：60fps\n- 输出格式：SVG动画\n\n### 3. 主要组成部分\n将所 有视觉元素分为5个类别：\n\n#### 3.1 人物元素\n- 严格使用输入中提供的人物资产\n- 保留完整的描述、位置和尺寸信息\n- 格式必须包含：描述、位置（asset路径） 、尺寸（根据宽高比计算的合适宽度和高度）\n\n#### 3.2 道具元素\n- 严格使用输入中提供的道具资产\n- 保留完整的描述、位置和尺寸信息\n- 格式必须包含：描述、 位置（asset路径）、尺寸（根据宽高比计算的合适宽度和高度）\n\n#### 3.3 背景设计\n- 创建适合动画概念的背景元素\n- 背景默认为白色\n- 只需简单描述，无需详细的位置和尺寸信息\n\n#### 3.4 文字关键词元素\n- 从输入的text中提取关键词\n- 只包含关键词，不包含完整字幕\n- 只需简单描述，无需详细的位置和尺寸信息\n\n#### 3.5 装饰元素\n- 创建增强视觉效果的装饰元素\n- 只需简单描述，无需详细的位置和尺寸信息\n\n### 4. 时间线结构（5秒动画）\n\n#### 4.1 轨道布局与分类\n- 将 所有元素按5个类别组织（背景、道具、人物、文字关键词、装饰元素）\n- **每个元素必须占据独立的轨道**，特别是每个人物元素和每个道具元素必须各自占据一条独立 轨道\n- 为每个轨道提供：\n  - 元素名称\n  - 中心点坐标（精确到像素）\n  - 持续时间范围（精确到0.1秒）\n  - 特殊动画效果（如适用）\n  - 元素在轨道上的变 化（位置、大小、透明度等）\n\n#### 4.2 视觉时间线表示\n- 创建ASCII艺术图表，显示5秒时间线上各元素的出现和消失\n- 格式参考：\n```\n时间(秒)  0.0   1.0   2.0   3.0   4.0   5.0\n          |     |     |     |     |     |\n基础背景   |=====================================|\n元素1     |===========|
    |========|\n元素2               |===============|\n...\n```\n\n#### 4.3 分场景时间线\n- 将5秒动画分为2-4个场景\n- 为每个场景提供时间范围和关键元素描述\n\n### 5. 布局分析\n\n#### 5.1 空间布局概览\n- 画布尺寸：1920×1080像素\n- 中心点：(960, 540)\n- 列出所有主要元素的位置坐标\n\n#### 5.2 资产素材详细信息\n- 为所有道具元素和人物元素提供详细信息：\n  - 位置（asset路径）\n  - 中心点坐标（精确到像素，例如(960, 540)）\n  - 尺寸（根据宽高比计算的合适宽度和 高度像素值）\n  - 特性描述\n  - 颜色信息（如适用）\n  - 初始位置和最终位置（如果元素在动画中移动）\n  - **确保所有素材之间不重叠**，合理安排每个元素的位置和大小\n\n#### 5.3 简化布局图示\n- 创建ASCII艺术图表，显示主要元素在画布上的位置分布\n- 确保图表清晰展示各元素的相对位置，验证元素之间没有重叠\n\n### 6. 视觉效果与技术实现\n\n#### 6.1 动画特效\n- 色彩处理：为主要元素定义颜色方案\n- SVG动画技术：列出使用的动画技术（不透明度、旋转、缩放、颜色过渡等）\n- 特殊效果：描述任何特殊视觉效果\n\n#### 6.2 技术实现要点\n- SVG动画实现方式\n- 响应式设计考虑\n- 性能优化建议\n- 素材引用实现：\n  - 对于所有引用的素材 （JPG/PNG或SVG动画）：统一使用`<image>`元素，例如：\n    ```xml\n    <image xlink:href="asset/props/example.jpg" x="起始X坐标" y="起始Y坐标" width="计算宽度" height="计算高度"/>\n    <image xlink:href="asset/characters/example.svg" x="起始X坐标" y="起始Y坐标" width="计算宽度" height="计算高度"/>\n    ```\n  - 根据素材的宽高比和画布布局，计算合适的宽度和高度，确保元素之间不重叠\n  - 高度必须根据aspectRatio和计算的宽度自动计算\n\n### 7. 重要说明\n\n#### 7.1 素材使用规范\n- 道具元素和人物元素必须严格使用项目asset目录下的对应素材\n- 其他元素（背景、文字、装饰）可根据描述自行生成\n- 素材引用必须遵循以下规 则：\n  - 所有素材必须通过其path属性引用，例如`asset/characters/trump_figure.svg`\n  - 所有素材必须保持原有比例，只调整整体大小\n  - 根据素材的宽高比和 画布布局，计算合适的宽度和高度\n  - 高度必须根据dimensions.aspectRatio和计算的宽度自动计算\n  - 所有引用的素材（JPG/PNG或SVG动画）必须统一使用`<image>` 元素引用\n  - 确保所有素材之间不重叠，合理安排每个元素的位置和大小\n\n#### 7.2 动画规范\n- 强调严格遵循5秒的时间线结构\n- 确保元素在指定时间点出现和消失\n- 保持动画流畅性和视觉连贯性\n- 确保文字关键词元素只包含关键词，不包含完整字幕\n\n## 特别注意事项\n\n1. 人物元素和道具元素必须使用项目asset目录下的素 材，禁止自行生成\n2. 每个人物和道具元素必须在时间线布局中单独作为一轨\n3. 素材布局需包含每个素材资产的详细信息（中心点坐标、宽高比、计算的宽度和高度、位置信息）\n4. 除了对话气泡外，画面中不应包含字幕和任何描述性的句子，只能出现关键词\n5. 所有元素的动画时间必须严格控制在5秒内\n6. 所有坐标必须精确到像素，时间必须精确到0.1秒\n7. 生成的SVG代码中必须严格按照提供的资产素材元素的path引用素材：\n   - 所有引用的素材（JPG/PNG或SVG动画）：统一使用`<image xlink:href="资产路径" width="计算宽度" height="计算高度"/>`元素\n8. 根据素材的宽高比和画布布局，计算合适的宽度和高度，确保元素之间不重叠\n9. 高度必须根据dimensions.aspectRatio和计算的宽度自动计算\n\n## 示例输出格式\n\n请参考以下格式创建完整的SVG动画设计文档：\n\n```markdown\n# "[概念名称]" 动画设计\n\n## 整体概念\n一个5秒钟的动画，视觉化表达概念："[输入的text内容]"\n\n## 画布尺寸与格式\n- 尺寸：1920×1080像素（16:9高清格式）\n- 帧率：60fps（确保平滑过渡）\n- 输出格式：SVG动画\n\n## 主要组成部分\n\n### 1. 人物元素\n[从输入的props中提取人物元素信息，包括描述、位置和根据宽高比计算的合适尺寸]\n\n### 2. 道具元素\n[从输入的props中提取道具元素信息，包括描述、位置和根据宽高比计算的合适尺寸]\n\n### 3. 背景设计\n[创建适合概念的背景元素描述]\n\n### 4. 文字关键词元素\n[ 从输入的text中提取关键词]\n\n### 5. 装饰元素\n[创建适合概念的装饰元素描述]\n\n## 时间线结构（5秒动画）\n\n### 轨道布局与分类\n[详细的轨道布局信息]\n\n### 视觉时间线表示\n[ASCII艺术时间线图表]\n\n### 分场景时间线\n[2-4个场景的详细描述]\n\n## 布局分析\n[详细的空间布局和资产信息，确保所有素材之间不重叠，根据宽高比计算合适的宽度和高度]\n\n## 视觉效果与技术实现\n[动画特效和技术实现详情]\n\n### SVG代码示例\n```xml\n<!-- 人物元素示例 -->\n<g id="trump" transform="translate(500, 540)">\n  <image xlink:href="asset/characters/trump_figure.svg" x="-140" y="-280" width="280" height="560"/>\n  <animate attributeName="opacity" values="0;1" begin="0s" dur="0.5s" fill="freeze"/>\n</g>\n\n<!-- 道具元素示例 -->\n<g id="microphone" transform="translate(400, 600)">\n  <image xlink:href="asset/props/microphone.svg" x="-40" y="-120" width="80" height="240"/>\n</g>\n```\n\n## 重要说明\n[素材使用和动画规范]\n```\n\n请确保 生成的文档完整、专业且符合SVG动画设计的最佳实践。文档应该提供足够的细节，使设计师和开发人员能够准确实现动画效果。特别注意素材引用部分，必须使用正确的`<image>`元素，并设置正确的宽度和高度。\n
    """,
        enable_thinking=True,
        thinking_budget_tokens=16000
    )
    print(f"响应: {response}")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())