from typing import List, Protocol
from langchain_core.messages import BaseMessage

class LLMProvider(Protocol):
    """
    LLM提供者协议

    所有AI提供者必须实现此协议中定义的方法
    """

    async def generate_response(self, messages: List[BaseMessage], enable_thinking: bool = False, thinking_budget_tokens: int = 16000) -> str:
        """
        生成响应

        Args:
            messages: 消息列表
            enable_thinking: 是否启用思考模式
            thinking_budget_tokens: 思考模式的token预算

        Returns:
            str: 生成的响应文本
        """
        ...

    def create_messages(self,
                       system_prompt: str,
                       history: List[BaseMessage],
                       query: str) -> List[BaseMessage]:
        """
        创建消息列表

        Args:
            system_prompt: 系统提示词
            history: 历史消息
            query: 用户查询

        Returns:
            List[BaseMessage]: 消息列表
        """
        ...
