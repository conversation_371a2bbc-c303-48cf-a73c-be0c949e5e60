import os
import time
from datetime import datetime

os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "generate-svg-demo-5.7"

from typing import TypedDict, Annotated, Sequence, Dict, Any, Callable, List
from langchain_core.messages import BaseMessage
import operator
import asyncio
import functools

from langgraph.graph import StateGraph, START, END
from dotenv import load_dotenv

from nodes.extract_hot_words_and_content import ExtractHotWordsAndContent
from nodes.analyze_5w1h import Analyze5W1H
from nodes.news_report_generator import NewsReportGenerator
# from nodes.asset_analyzer import AssetAnalyzer
from nodes.director_script_generator import DirectorScriptGenerator
from nodes.director_editor_worker import DirectorEditorWorker
from nodes.process_asset_image import ProcessAssetImage
from nodes.svg_prompt_generator import SVGPromptGenerator
from nodes.svg_generator import SVGGenerator
from nodes.svg_code_checker import SVGCodeChecker

# 加载环境变量
load_dotenv()

# 定义状态类型
class WorkflowState(TypedDict):
    """工作流状态"""
    messages: Annotated[Sequence[BaseMessage], operator.add]
    current_step: str
    data: dict
    execution_times: Dict[str, float]  # 记录每个节点的执行时间

def time_execution(node_name: str, func: Callable) -> Callable:
    """
    装饰器函数，用于记录节点执行时间

    Args:
        node_name: 节点名称
        func: 节点处理函数

    Returns:
        包装后的函数
    """
    @functools.wraps(func)
    async def wrapper(state: Dict[str, Any]) -> Dict[str, Any]:
        # 确保execution_times字段存在
        if "execution_times" not in state:
            state["execution_times"] = {}

        # 记录开始时间
        start_time = time.time()

        # 执行原函数，根据函数类型选择同步或异步调用
        if asyncio.iscoroutinefunction(func):
            # 如果是异步函数，使用await调用
            result = await func(state)
        else:
            # 如果是同步函数，直接调用
            result = func(state)

        # 计算执行时间并记录
        execution_time = time.time() - start_time
        result["execution_times"][node_name] = execution_time

        return result

    return wrapper

def create_workflow() -> StateGraph:
    """创建工作流图"""
    # 初始化节点
    extract_hot_words_and_content = ExtractHotWordsAndContent()
    analyze_5w1h = Analyze5W1H()
    news_report_generator = NewsReportGenerator()
    # asset_analyzer = AssetAnalyzer()
    director_script_generator = DirectorScriptGenerator()
    director_editor_worker = DirectorEditorWorker()
    svg_prompt_generator = SVGPromptGenerator()
    # 使用正确的配置文件路径
    svg_generator = SVGGenerator(conf_path="conf/conf.ini")
    process_asset_image = ProcessAssetImage()
    svg_code_checker = SVGCodeChecker()

    # 创建工作流图
    workflow = StateGraph(WorkflowState)

    # 添加节点，并包装执行时间记录功能
    workflow.add_node("extract_hot_words_and_content",
                    time_execution("extract_hot_words_and_content", extract_hot_words_and_content.process))
    workflow.add_node("analyze_5W1H",
                    time_execution("analyze_5W1H", analyze_5w1h.process))
    workflow.add_node("news_report_generator",
                    time_execution("news_report_generator", news_report_generator.process))
    # workflow.add_node("asset_analyzer",
    #                  time_execution("asset_analyzer", asset_analyzer.process))
    workflow.add_node("process_asset_image",
                    time_execution("process_asset_image", process_asset_image.process))
    workflow.add_node("director_script_generator",
                    time_execution("director_script_generator", director_script_generator.process))
    workflow.add_node("director_editor_worker",
                    time_execution("director_editor_worker", director_editor_worker.process))
    workflow.add_node("generate_svg_prompt",
                    time_execution("generate_svg_prompt", svg_prompt_generator.process))
    workflow.add_node("generate_svg",
                    time_execution("generate_svg", svg_generator.process))
    workflow.add_node("svg_code_checker",
                    time_execution("svg_code_checker", svg_code_checker.process))

    # 添加边
    workflow.add_edge(START, "extract_hot_words_and_content")
    workflow.add_edge("extract_hot_words_and_content", "analyze_5W1H")
    workflow.add_edge("analyze_5W1H", "news_report_generator")
    # workflow.add_edge("news_report_generator", "asset_analyzer")
    # workflow.add_edge("asset_analyzer", "process_asset_image")
    workflow.add_edge("news_report_generator", "process_asset_image")
    workflow.add_edge("process_asset_image", "director_script_generator")
    workflow.add_edge("director_script_generator", "director_editor_worker")
    workflow.add_edge("director_editor_worker", "generate_svg_prompt")
    workflow.add_edge("generate_svg_prompt", "generate_svg")
    workflow.add_edge("generate_svg", END)
    # workflow.add_edge("svg_code_checker", END)

    return workflow.compile()

def format_time(seconds: float) -> str:
    """
    将秒数格式化为可读的时间格式

    Args:
        seconds: 秒数

    Returns:
        格式化后的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.2f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds:.2f}秒"
    else:
        hours = int(seconds // 3600)
        remaining = seconds % 3600
        minutes = int(remaining // 60)
        remaining_seconds = remaining % 60
        return f"{hours}小时{minutes}分{remaining_seconds:.2f}秒"

async def main():
    """主函数"""
    print("\n" + "*"*80)
    print("*" + " "*30 + "开始执行工作流" + " "*30 + "*")
    print("*"*80 + "\n")

    # 记录总体开始时间
    total_start_time = time.time()

    # 创建工作流
    workflow = create_workflow()
    print("工作流图已创建，包含以下节点:")
    print("- extract_hot_words_and_content: 提取热词和内容")
    print("- analyze_5W1H: 分析5W1H数据")
    print("- news_report_generator: 生成新闻报告")
    print("- process_asset_image: 处理资产图片")
    print("- director_script_generator: 生成导演剧本")
    print("- director_editor_worker: 导演剪辑讨论")
    print("- generate_svg_prompt: 生成SVG提示词")
    print("- generate_svg: 生成SVG动画")
    print("- svg_code_checker: SVG代码和prompt一致性检查")
    print("\n开始执行工作流...\n")

    # 初始状态
    initial_state = {
        "messages": [],
        "current_step": "start",
        "data": {},
        "execution_times": {}
    }

    # 执行工作流
    result = await workflow.ainvoke(initial_state)

    # 计算总执行时间
    total_execution_time = time.time() - total_start_time

    print("\n" + "*"*80)
    print("*" + " "*30 + "工作流执行完成!" + " "*29 + "*")
    print("*"*80)
    print(f"\n最终状态: current_step = {result['current_step']}")
    print(f"数据处理节点数: {len(result['data'])}")
    print(f"消息数量: {len(result['messages'])}")

    # 输出每个节点的执行时间
    print("\n" + "="*50)
    print("各节点执行时间统计:")
    print("="*50)

    # 获取执行时间
    execution_times = result.get("execution_times", {})

    # 按执行顺序输出节点执行时间
    node_order = [
        "extract_hot_words_and_content",
        "analyze_5W1H",
        "news_report_generator",
        "process_asset_image",
        "director_script_generator",
        "director_editor_worker",
        "generate_svg_prompt",
        "generate_svg",
        "svg_code_checker"
    ]

    # 计算最长节点名称的长度，用于对齐输出
    max_name_length = max(len(name) for name in node_order)

    # 输出每个节点的执行时间
    for node_name in node_order:
        if node_name in execution_times:
            time_taken = execution_times[node_name]
            formatted_time = format_time(time_taken)
            print(f"{node_name.ljust(max_name_length)} : {formatted_time}")

    # 输出总执行时间
    print("-"*50)
    print(f"{'总执行时间'.ljust(max_name_length)} : {format_time(total_execution_time)}")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())