**AI任务指令：生成动画设计文档**

**你的角色：** 你是一名专业的动画设计师助理。你的任务是仔细阅读并深刻理解提供的"视频导演和剪辑师的聊天记录"（下文简称"聊天记录"），并基于此记录，严格按照以下模板和要求，生成一份详尽的SVG动画设计文档。这份文档将作为后续制作SVG动画的唯一蓝本。

**核心要求：**
1.  **信息来源唯一性与优先级：** 文档中的所有信息（包括时长、元素、位置、动画效果、时间线等）**必须**严格从提供的"聊天记录"中提取、推断或总结。
    *   **处理导演反馈：**
        *   **当导演回复"不需要调整"时：**
            *   剪辑师提交的"视频剪辑方案"被视为**完全采纳**，所有细节应严格遵照此方案进行设计。
            *   剪辑师在"缺失素材建议"和"征询导演意见"中提出的所有建议和问题均被视为**隐性拒绝**（即不予采纳），因此在生成动画设计文档时**不应包含或采纳**这些建议或提出的额外需求。
        *   **当导演给出具体反馈（非"不需要调整"）时：**
            *   严格根据导演的**明确指示**来修改或采纳剪辑师的"视频剪辑方案"、"缺失素材建议"或"征询导演意见"中**被采纳**的部分。
            *   **忽略**导演未提及或明确拒绝的建议。
2. **精确性：** 所有元素位置精确到像素，时间精确到0.1秒。
3. **完整性：** 尽可能完整地填写模板的每一部分。如果聊天记录中未明确提及某些细节（如字体、具体缓动函数等），请在相应位置注明"[聊天记录未提及，建议默认值或留空]"或提出合理建议。

**输出格式：** 按照以下结构和指示填充的动画设计文档。

---

**动画设计文档**

## 1. 整体概念与目标
*   **动画总时长：** `[从聊天记录中精确提取，单位：秒，例如：15.3秒]`
*   **动画核心内容/叙事：** `[根据聊天记录总结动画需要展示的关键信息、故事或音频内容的主旨]`
*   **动画风格与目的（若聊天记录提及）：** `[例如：信息图表式、幽默风趣、产品功能演示、品牌宣传等。若无，则注明"聊天记录未明确"]`

## 2. 画布尺寸与输出格式
*   **画布尺寸：** 1920×1080像素 (宽高比 16:9，高清格式)
*   **最终输出格式：** SVG动画 (内联SMIL或CSS动画，引用外部图片素材)

## 3. 主要组成部分

### 3.1 人物元素列表
`[指示：根据聊天记录，列出最终版本[视频剪辑方案]中[图片素材使用]出现的人物元素。对于每个人物元素：]`
*   **人物名称/代号：** `[聊天记录中对此人物的称呼]`
    *   **描述：** `[简要描述人物特征、角色，依据聊天记录]`
    *   **资产路径：** `[聊天记录中指定的xxx.png/jpg路径]`
    *   **宽高比：** `[宽度] / [高度] 保留两位小数，例如：1.33 表示 4:3 比例`
    
### 3.2 道具元素列表
`[指示：根据聊天记录，列出最终版本[视频剪辑方案]中[图片素材使用]出现的道具元素。对于每个道具元素：]`
*   **道具名称/代号：** `[聊天记录中对此道具的称呼]`
    *   **描述：** `[简要描述道具功能、外观，依据聊天记录]`
    *   **资产路径：** `[聊天记录中指定的xxx.png/jpg路径]`
    *   **宽高比：** `[宽度] / [高度] 保留两位小数，例如：1.33 表示 4:3 比例`

### 3.3 背景设计
*   **背景类型：** `[从聊天记录提取，例如：纯色、渐变、图片背景]`
*   **颜色/资产路径：** `[若是纯色，填写色值如#FFFFFF；若是图片，填写资产路径；若是渐变，描述渐变参数]`
*   **动画效果（若有）：** `[例如：颜色渐变、背景滚动等，并注明时间范围]`

### 3.4 文字关键词元素
`[指示：根据聊天记录，列出列出最终版本[视频剪辑方案]中动态或静态显示的文字。对于每个文字元素：]`
*   **文字内容：** "`[聊天记录中指定的文字]`"
    *   **出现时间范围：** `[开始秒数]s - [结束秒数]s`
    *   **位置（锚点或左上角坐标）：** `(x, y)`
    *   **字体：** `[聊天记录中提及的字体名称，若无则注明"聊天记录未提及，建议使用无衬线体如Arial/Helvetica"]`
    *   **字号：** `[聊天记录中提及的字号，或根据画布和重要性建议]`
    *   **颜色：** `[聊天记录中提及的颜色，或建议颜色]`

## 4. 分镜/场景与音频同步列表
`[指示：根据聊天记录中提及的音频/独白内容，将其与对应时间段的视觉表现关联起来。严格遵守以下详细布局和元素约束。视频画布尺寸为14个宽度单位 x 8个高度单位，中心点为(0,0)。X轴范围为-7到+7 (左负右正)，Y轴范围为-4到+4 (下负上正)。]`

*   **时间戳：** `0.0s ~ X.Xs`
    *   **音频/独白内容：** "`[聊天记录中该时间段的精确音频文本或描述]`"
    *   **涉及元素 (按出场顺序列出)：** `[按此时间戳内元素出现的先后顺序列出其名称。这些元素必须且仅能来自"3.1 人物元素列表"、"3.2 道具元素列表"和"3.4 文字关键词元素列表"。不要出现这些列表之外的元素。]`
        *   `示例：人物A, 道具X, 文字关键词Y`
    *   **视觉构成与布局 (基于14x8单位网格，中心点(0,0))：**
        `[指示：按以下步骤描述画面构成。确保"涉及元素"中列出的所有元素都得到体现。]`
        1.  **主要人物/道具元素 (可有多个，按重要性或视觉中心依次描述)：**
            *   **元素名称 (来自"涉及元素")：** `[主要元素1的名称]`
            *   **视觉参考：** `[例如：人物A的正面微笑特写，道具X的完整展示图]`
            *   **尺寸 (宽x高)：** `[数值]x[数值] 单位` (例如：`4x3 单位`)
            *   **位置 (X,Y 相对于视频中心点(0,0))：** `[X坐标], [Y坐标] 单位` (例如：`-2, 1` 表示中心点左2单位、上1单位)
            *   **(若有更多主要元素，重复以上四项)**
        2.  **次要人物/道具元素 (可有多个，按重要性或相对位置依次描述)：**
            *   **元素名称 (来自"涉及元素")：** `[次要元素1的名称]`
            *   **视觉参考：** `[例如：背景中的人物B模糊身影，角落的道具Y]`
            *   **尺寸 (宽x高)：** `[数值]x[数值] 单位`
            *   **位置 (X,Y)：**
                *   `相对于视频中心点(0,0)：[X坐标], [Y坐标] 单位`
                *   `或 相对于[某主要元素名称]：[描述，例如："在[主要元素A]右侧2个单位处，下方1个单位处"]`
            *   **(若有更多次要元素，重复以上四项)**
        3.  **文字关键词元素 (按出场或重要性依次描述)：**
            *   **元素名称 (来自"涉及元素")：** `[文字关键词1的名称]`
            *   **显示文本：** `[实际显示的文字内容]`
            *   **位置：** `[描述相对于哪个图片元素或绝对坐标(X,Y)，例如："在[主要元素A]下方"，"位于(5, -3)"]`
            *   **字体/大小/颜色 (可选)：** `[例如：思源黑体，中等，黑色]`
            *   **(若有更多文字关键词元素，重复以上四项)**
    *   **动画与状态描述：** `[简述此时间段内画面上"涉及元素"中各元素的状态、正在发生的关键动画、交互。例如：[人物A]从左侧淡入，[道具X]放大后缩小，[文字关键词Y]逐字显示在[人物A]的对话气泡中。]`


## 5. 重要说明与约束
*   **素材引用：** 所有图片素材严格使用 `3.1` 和 `3.2` 中提取的 `资产路径`。
*   **坐标系：** 
    * 画布尺寸：14个宽度单位 × 8个高度单位
    * 坐标系原点：画布中心点(0,0)
    * X轴范围：-7到+7（左负右正）
    * Y轴范围：-4到+4（下负上正）
    * 所有元素位置必须基于此中心坐标系进行定位
*   **尺寸与比例：** 所有的图片素材只给出了宽高比，具体尺寸需要根据实际画布大小和布局计算出高度，宽度自适应。
*   **元素不重叠：** 除非设计意图或聊天记录明确指示，否则应避免动画元素在视觉上不期望地重叠。
*   **动画平滑度：** 动画应尽可能平滑，帧率感觉应在24fps以上（虽然SVG动画不直接指定帧率，但效果应流畅）。


**下方为输入的聊天记录：**